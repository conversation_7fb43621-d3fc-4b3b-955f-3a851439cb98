'use client';

import React, { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import dynamic from 'next/dynamic';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';
import InputField from '../components/Inputs/InputField';
import { DndContext, closestCenter } from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useDispatch, useSelector } from 'react-redux';
import {
  getBrandsAsync,
  getProductsAsync,
  updateProductPreferencesAsync,
} from '@/store/api/productApi';
import DefaultImage from '../../public/images/default-Image.jpg';
import { onboardingUserAsync } from '@/store/api/onboardingApi';
import { ProductType } from '@/utils/constant';
import Breadcrumb from '../components/Inputs/Breadcrumb';
import FilterField from '../components/table/FilterField';
import SortableItem from '../components/table/SortableItem';

const ProductTable = dynamic(() => import('../components/table/ProductTable'), {
  ssr: false,
});


const Products = () => {
  const [products, setProducts] = useState([]);
  const [filterText, setFilterText] = useState('');
  const [debouncedText, setDebouncedText] = useState(filterText);
  const [selectedTag, setSelectedTag] = useState('');
  const [selectedBrand, setSelectedBrand] = useState('');
  const [brands, setBrands] = useState([]);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [displayProperties, setDisplayProperties] = useState({
    product_name: true,
    id: true,
    brand: true,
    sku: true,
    status: true,
    price: true,
    variants: true,
    quantity: true,
  });
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [createProductOpen, setCreateProductOpen] = useState(false);
  const [isCategoryOpen, setIsCategoryOpen] = useState(false);
  const [isBrandOpen, setIsBrandOpen] = useState(false);
  const [categories, setCategories] = useState([]);
  const statusDropdownRef = useRef(null);
  const displayMenuRef = useRef(null);
  const categoryDropdownRef = useRef(null);
  const brandDropdownRef = useRef(null);
  const createProductRef = useRef(null);
  const [items, setItems] = useState(Object.keys(displayProperties));
  const [selectedStatus, setSelectedStatus] = useState('');
  const [viewType, setViewType] = useState('table'); // 'table' or 'grid'
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  // const [columns, setColumns] = useState([]);
  const [activeTab, setActiveTab] = useState('All');
  const { onboardingData } = useSelector((state) => state.onboarding);
  const [pagination, setPagination] = useState({
    page: 1,
    perPage: 10,
    total: 0,
  });

  const dispatch = useDispatch();
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setDebouncedText(filterText);
    }, 300);

    return () => clearTimeout(timeout);
  }, [filterText]);

  useEffect(() => {
    const getBrands = async () => {
      const { payload } = await dispatch(getBrandsAsync());
      setBrands(payload.data?.map((e) => ({ value: e.id, label: e.name })));
    };
    getBrands();
    const getTags = async () => {
      const { payload } = await dispatch(onboardingUserAsync());
      setCategories(
        payload?.data?.product_categories?.map((item) => {
          return { value: item.id, label: item.name };
        })
      );
    };
    getTags();
  }, []);
  // fetch products when filter changes
  useEffect(() => {
    getProducts();
  }, [
    debouncedText,
    activeTab,
    selectedTag,
    selectedBrand,
    pagination.page,
    pagination.perPage,
  ]);

  async function getProducts() {
    const { payload } = await dispatch(
      getProductsAsync({
        page: pagination.page,
        per_page: pagination.perPage,
        [`filter[product_type]`]: ProductType?.RETAIL,
        ...(debouncedText ? { 'filter[search]': debouncedText } : {}),
        ...(activeTab !== 'All' ? { 'filter[status]': activeTab } : {}),
        ...(selectedBrand ? { 'filter[brand]': selectedBrand?.value } : {}),
        ...(selectedTag ? { 'filter[category]': selectedTag?.value } : {}),
      })
    );
    setProducts(payload);
    setPagination((prev) => ({
      ...prev,
      total: payload?.meta?.total || 0,
    }));
    const visibleKeys = payload?.preferences?.visible_columns || null;
    // If visibleKeys is not available (null/undefined), set all to true
    const updatedDisplayProps = visibleKeys
      ? Object.fromEntries(
          Object.keys(displayProperties).map((key) => [
            key,
            visibleKeys.includes(key),
          ])
        )
      : Object.fromEntries(
          Object.keys(displayProperties).map((key) => [key, true])
        );

    // setColumns(visibleKeys || Object.keys(displayProperties));
    setDisplayProperties(updatedDisplayProps);

    setDisplayProperties(updatedDisplayProps);
  }

  // Update the click outside effect to handle both dropdowns
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        statusDropdownRef.current &&
        !statusDropdownRef.current.contains(event.target)
      ) {
        setIsFilterOpen(false);
      }
      if (
        displayMenuRef.current &&
        !displayMenuRef.current.contains(event.target)
      ) {
        setIsDisplayMenuOpen(false);
      }
      if (
        createProductRef.current &&
        !createProductRef.current.contains(event.target)
      ) {
        setCreateProductOpen(false);
      }
      if (
        categoryDropdownRef.current &&
        !categoryDropdownRef.current.contains(event.target)
      ) {
        setIsCategoryOpen(false);
      }
      if (
        brandDropdownRef.current &&
        !brandDropdownRef.current.contains(event.target)
      ) {
        setIsBrandOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // const categorie = ['Furniture', 'Clothing'];
  const tags = ['Published', 'Pending Approval', 'Draft', 'Archive'];

  const data = products?.data?.map((data) => {
    return {
      archieveId: data?.id,
      editId: data?.id,
      productType: data?.product_type,
      id: data?.product_id,
      name: data?.name,
      image: data?.images?.main_image,
      category: data?.category,
      brand: data?.brand,
      brand_manufacturer: data?.brand_manufacturer?.name,
      model_number: data?.model_number,
      sku:
        data?.variations?.length > 0
          ? data?.variations?.find((v) => v.is_default)?.label
          : data?.variations?.[0]?.label,
      status: data?.status,
      variants: (data?.is_variation && data?.variations?.length) || 0,
      base_price: data?.base_price || 0,
      is_variation: data?.is_variation,
      qty: data?.qty,
      variantOptions: data?.variations?.map((variant, index) => {
        return {
          // Combine the attribute names for each variant
          name: variant?.attributes
            ?.map((attr) => attr?.attribute_name)
            .join(' / '),
          id: variant?.id, // Unique ID for each variant
        };
      }),
    };
  });

  // Add this new handler
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);

        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);

        return newItems;
      });
    }
  };


  const tabs = [
    { id: 'All', count: null },
    { id: 'Published', count: products?.status_counts?.Published },
    {
      id: 'Pending Approval',
      count: products?.status_counts?.['Pending Approval'],
    },
    { id: 'Draft', count: products?.status_counts?.Draft },
    { id: 'Archive', count: products?.status_counts?.Archive },
  ];
  // const filteredOrders = orders.filter(order =>
  //   activeTab === 'All' ? true : order.status === activeTab
  // );
  const getStatusColor = (status) => {
    const colors = {
      Published: 'bg-green-500/10 text-green-500',
      'Pending Approval': 'bg-info-500/10 text-info-500',
      Draft: 'bg-gray-500/10 text-gray-500',
      Archive: 'bg-warning-500/10 text-warning-500',
    };
    return colors[status] || colors['All'];
  };

  const toggleColumnVisibility = async (column) => {
    // Update the display properties
    const newDisplayProperties = {
      ...displayProperties,
      [column]: !displayProperties[column],
    };
    setDisplayProperties(newDisplayProperties);

    // Prepare the payload for the API call
    const payload = preparePayload(newDisplayProperties);

    // Make the API call
    const res = await dispatch(
      updateProductPreferencesAsync({
        pageIdentifier: 'product_listing', // Replace with the actual identifier for the page
        ...payload, // Send the data as an object
      })
    );
    if (res?.data?.success) {
      getProducts();
    }
  };

  // Function to prepare the payload with only visible columns
  const preparePayload = (newDisplayProperties) => {
    const visibleColumns = Object.keys(newDisplayProperties).filter(
      (key) => newDisplayProperties[key] === true
    );
    return {
      view_type: 'grid', // or any other view type you need
      visible_columns: visibleColumns,
    };
  };
  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Product Management' },
    { label: 'Manage Products' }
  ];
  // Function to simulate sending the payload (API call)

  return (
    <PrivateLayout>
      <div className="flex bg-surface-100 rounded-xl ml-3 w-[calc(100% - 12px)] mt-[60px]">
        {/* Add this state at the top of the Products component */}

        {/* Update the sidebar component to pass the state */}
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        {/* Update the content wrapper div className */}
        <div
          className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full p-6 mx-auto rounded-s-xl rounded-bl-xl overflow-auto h-[calc(100vh-60px)] transition-base`}
        >
          <div className="flex justify-between items-center mb-6">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Manage Products</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
            <div className="flex gap-1.5">
              <button className="btn btn-gray inline-flex items-center gap-2">
                <span className="icon icon-upload-simple  font-bold text-lg" />
                Export
              </button>
              <button className="btn btn-gray inline-flex items-center gap-2"><span className="icon icon-download-simple font-bold text-lg" />Import</button>
              <Link href="/manage-products/add-product" className="btn inline-flex items-center gap-2">
                <span className="icon icon-plus font-bold text-lg" />
                Add Product
              </Link>
            </div>
          </div>

          <div className="rounded-xl">
            <FilterField
              filterText={filterText}
              setFilterText={setFilterText}
              isSearchFilterOpen={isSearchFilterOpen}
              setIsSearchFilterOpen={setIsSearchFilterOpen}
              isDisplayMenuOpen={isDisplayMenuOpen}
              setIsDisplayMenuOpen={setIsDisplayMenuOpen}
              displayMenuRef={displayMenuRef}
            >
              <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
                <DndContext
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext
                    items={items}
                    strategy={verticalListSortingStrategy}
                  >
                    <ul className="space-y-1">
                      {items.map((key) => (
                        <SortableItem
                          key={key}
                          id={key}
                          value={key}
                          checked={displayProperties[key]}
                          onChange={() =>
                            setDisplayProperties((prev) => ({
                              ...prev,
                              [key]: !prev[key],
                            }))
                          }
                        />
                      ))}
                    </ul>
                  </SortableContext>
                </DndContext>
              </div>
            </FilterField>

            {viewType === 'table' ? (
              <ProductTable
                // data={products}
                data={data}
                filterText={filterText}
                selectedStatus={selectedStatus}
                displayProperties={displayProperties}
                activeTab={activeTab}
                DefaultImage={DefaultImage}
                getProducts={getProducts}
                pagination={pagination}
                setPagination={setPagination}
              />
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6 gap-4 bg-white p-4 rounded-bl-xl rounded-br-xl max-h-[calc(100vh-218px)] overflow-y-auto border border-border-color border-t-0">
                {/* Grid view implementation */}
                {data
                  .filter(
                    (item) =>
                      item.name
                        .toLowerCase()
                        .includes(filterText.toLowerCase()) ||
                      item.sku.toLowerCase().includes(filterText.toLowerCase())
                  )
                  .map((item) => (
                    <div
                      key={item.id}
                      className="border border-border-color rounded-xl p-3 hover:shadow-[0px_25px_60px_-10px_#1C273121] transition-base"
                    >
                      <div className="aspect-square bg-[#FAFAFA] rounded-xl overflow-hidden mb-3.5">
                        <Image
                          src={item?.image || DefaultImage}
                          alt={item?.name}
                          width={345}
                          height={345}
                          className="object-contain w-full h-full rounded-xl overflow-hidden"
                        />
                      </div>
                      <h3 className="text-sm font-medium">
                        {item?.name || '-'}
                      </h3>
                      <p className="text-xs text-gray-400 font-medium capitalize">
                        {item?.category?.name || '-'}
                      </p>
                      <div className="flex justify-between items-center mt-2">
                        <span className="font-bold text-sm">
                          <span className="font-medium">
                            ${item?.base_price ? item?.base_price : 0}
                          </span>
                        </span>
                        <span
                          className={`inline-flex items-center gap-1.5 px-2 py-1 rounded-lg text-xs font-bold text-nowrap ${
                            item.status === 'Pending Approval'
                              ? 'bg-success-500/10 text-success-600'
                              : item.status === 'Pending Approval'
                                ? 'bg-info-500/10 text-info-600'
                                : item.status === 'Archive'
                                  ? 'bg-warning-500/10 text-warning-600'
                                  : 'bg-gray-500/10 text-gray-600'
                          }`}
                        >
                          {item.status || '-'}
                        </span>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
};

export default Products;
