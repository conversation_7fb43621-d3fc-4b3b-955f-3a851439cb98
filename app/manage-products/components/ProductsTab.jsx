'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import DataTable from 'react-data-table-component';
import { Tooltip } from 'react-tooltip';
import FilterField from '../../components/table/FilterField';
import ProductDetailModal from './ProductDetailModal';
import AddProductModal from './AddProductModal';

const ProductsTab = () => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isProductDetailOpen, setIsProductDetailOpen] = useState(false);
  const [isAddProductOpen, setIsAddProductOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [activeStatusTab, setActiveStatusTab] = useState('All');
  const displayMenuRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    productName: true,
    productCode: true,
    category: true,
    brand: true,
    productType: true,
    status: true,
    price: true,
    inventory: true,
    variants: true,
  });

  // Sample data - in real app this would come from API
  const [products, setProducts] = useState([
    {
      id: 'PRD-001',
      productName: 'Wireless Bluetooth Headphones',
      productCode: 'WBH-001',
      productType: 'Simple',
      category: { name: 'Electronics', id: 'cat-1' },
      brand: { name: 'TechSound', id: 'brand-1' },
      status: 'Published',
      mrp: 199.99,
      sellingPrice: 149.99,
      costPrice: 89.99,
      inventory: 150,
      minOrderQty: 1,
      variants: 0,
      tags: ['wireless', 'bluetooth', 'audio'],
      thumbnail: '/images/headphones.jpg',
      shortDescription: 'High-quality wireless headphones with noise cancellation',
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-20T14:20:00Z'
    },
    {
      id: 'PRD-002',
      productName: 'Cotton T-Shirt',
      productCode: 'CTS-002',
      productType: 'Variant',
      category: { name: 'Clothing', id: 'cat-2' },
      brand: { name: 'ComfortWear', id: 'brand-2' },
      status: 'Published',
      mrp: 29.99,
      sellingPrice: 24.99,
      costPrice: 12.99,
      inventory: 500,
      minOrderQty: 1,
      variants: 12,
      tags: ['cotton', 'casual', 'comfortable'],
      thumbnail: '/images/tshirt.jpg',
      shortDescription: '100% cotton comfortable t-shirt available in multiple colors and sizes',
      createdAt: '2024-01-10T09:15:00Z',
      updatedAt: '2024-01-18T16:45:00Z'
    },
    {
      id: 'PRD-003',
      productName: 'Office Chair Bundle',
      productCode: 'OCB-003',
      productType: 'Bundle',
      category: { name: 'Furniture', id: 'cat-3' },
      brand: { name: 'ErgoDesk', id: 'brand-3' },
      status: 'Draft',
      mrp: 399.99,
      sellingPrice: 349.99,
      costPrice: 199.99,
      inventory: 25,
      minOrderQty: 1,
      variants: 0,
      tags: ['office', 'ergonomic', 'bundle'],
      thumbnail: '/images/office-chair.jpg',
      shortDescription: 'Ergonomic office chair with lumbar support and accessories',
      createdAt: '2024-01-12T11:00:00Z',
      updatedAt: '2024-01-19T13:30:00Z'
    },
    {
      id: 'PRD-004',
      productName: 'Smartphone Case',
      productCode: 'SPC-004',
      productType: 'Variant',
      category: { name: 'Accessories', id: 'cat-4' },
      brand: { name: 'ProtectTech', id: 'brand-4' },
      status: 'Pending Approval',
      mrp: 19.99,
      sellingPrice: 15.99,
      costPrice: 7.99,
      inventory: 300,
      minOrderQty: 1,
      variants: 8,
      tags: ['protection', 'smartphone', 'durable'],
      thumbnail: '/images/phone-case.jpg',
      shortDescription: 'Durable smartphone case with drop protection',
      createdAt: '2024-01-14T14:20:00Z',
      updatedAt: '2024-01-21T10:15:00Z'
    },
    {
      id: 'PRD-005',
      productName: 'Gaming Laptop',
      productCode: 'GL-005',
      productType: 'Simple',
      category: { name: 'Computers', id: 'cat-5' },
      brand: { name: 'GameForce', id: 'brand-5' },
      status: 'Archive',
      mrp: 1299.99,
      sellingPrice: 1199.99,
      costPrice: 899.99,
      inventory: 5,
      minOrderQty: 1,
      variants: 0,
      tags: ['gaming', 'laptop', 'high-performance'],
      thumbnail: '/images/gaming-laptop.jpg',
      shortDescription: 'High-performance gaming laptop with RTX graphics',
      createdAt: '2024-01-08T16:45:00Z',
      updatedAt: '2024-01-16T12:00:00Z'
    }
  ]);

  const statusTabs = [
    { id: 'All', label: 'All Products', count: products.length },
    { id: 'Published', label: 'Published', count: products.filter(p => p.status === 'Published').length },
    { id: 'Draft', label: 'Draft', count: products.filter(p => p.status === 'Draft').length },
    { id: 'Pending Approval', label: 'Pending Approval', count: products.filter(p => p.status === 'Pending Approval').length },
    { id: 'Archive', label: 'Archive', count: products.filter(p => p.status === 'Archive').length }
  ];

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.productName.toLowerCase().includes(filterText.toLowerCase()) ||
                         product.productCode.toLowerCase().includes(filterText.toLowerCase()) ||
                         product.brand.name.toLowerCase().includes(filterText.toLowerCase());
    const matchesStatus = activeStatusTab === 'All' || product.status === activeStatusTab;
    return matchesSearch && matchesStatus;
  });

  const columns = [
    {
      name: 'Product',
      selector: row => row.productName,
      sortable: true,
      cell: (row) => (
        <div className="flex items-center gap-3 py-2">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <span className="icon icon-package text-gray-400" />
          </div>
          <div>
            <div className="font-medium text-sm">{row.productName}</div>
            <div className="text-xs text-gray-500">{row.productCode}</div>
          </div>
        </div>
      ),
      width: '250px',
    },
    {
      name: 'Type',
      selector: row => row.productType,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
          row.productType === 'Simple' ? 'bg-blue-100 text-blue-800' :
          row.productType === 'Variant' ? 'bg-purple-100 text-purple-800' :
          'bg-orange-100 text-orange-800'
        }`}>
          {row.productType}
        </span>
      ),
      width: '100px',
    },
    {
      name: 'Category',
      selector: row => row.category.name,
      sortable: true,
      width: '120px',
    },
    {
      name: 'Brand',
      selector: row => row.brand.name,
      sortable: true,
      width: '120px',
    },
    {
      name: 'Price',
      selector: row => row.sellingPrice,
      sortable: true,
      cell: (row) => (
        <div className="text-sm">
          <div className="font-medium">${row.sellingPrice}</div>
          {row.mrp !== row.sellingPrice && (
            <div className="text-xs text-gray-500 line-through">${row.mrp}</div>
          )}
        </div>
      ),
      width: '100px',
    },
    {
      name: 'Inventory',
      selector: row => row.inventory,
      sortable: true,
      cell: (row) => (
        <span className={`font-medium ${
          row.inventory < 10 ? 'text-red-600' : 
          row.inventory < 50 ? 'text-yellow-600' : 
          'text-green-600'
        }`}>
          {row.inventory}
        </span>
      ),
      width: '100px',
    },
    {
      name: 'Variants',
      selector: row => row.variants,
      sortable: true,
      cell: (row) => (
        <span className="text-sm">
          {row.variants > 0 ? `${row.variants} variants` : 'No variants'}
        </span>
      ),
      width: '120px',
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          row.status === 'Published' ? 'bg-green-100 text-green-800' :
          row.status === 'Draft' ? 'bg-gray-100 text-gray-800' :
          row.status === 'Pending Approval' ? 'bg-yellow-100 text-yellow-800' :
          'bg-red-100 text-red-800'
        }`}>
          {row.status}
        </span>
      ),
      width: '140px',
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewProduct(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          <Link
            href={`/manage-products/edit/${row.id}`}
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit Product"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-pencil-line text-base" />
          </Link>
          <button
            onClick={() => handleCloneProduct(row)}
            data-tooltip-id="clone-tooltip"
            data-tooltip-content="Clone Product"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-info-500/10 hover:text-info-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-copy text-base" />
          </button>
          <button
            onClick={() => handleDeleteProduct(row.id)}
            data-tooltip-id="delete-tooltip"
            data-tooltip-content="Delete Product"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-trash text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="edit-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="clone-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="delete-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      width: '160px',
    },
  ];

  const handleViewProduct = (product) => {
    setSelectedProduct(product);
    setIsProductDetailOpen(true);
  };

  const handleCloneProduct = (product) => {
    const clonedProduct = {
      ...product,
      id: `PRD-${Date.now()}`,
      productName: `${product.productName} (Copy)`,
      productCode: `${product.productCode}-COPY`,
      status: 'Draft'
    };
    setProducts(prev => [...prev, clonedProduct]);
    console.log('Product cloned:', clonedProduct);
  };

  const handleDeleteProduct = (productId) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      setProducts(prev => prev.filter(p => p.id !== productId));
      console.log('Product deleted:', productId);
    }
  };

  const handleAddProduct = (productData) => {
    const newProduct = {
      ...productData,
      id: `PRD-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setProducts(prev => [...prev, newProduct]);
    console.log('Product added:', newProduct);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    rows: {
      style: {
        fontSize: '14px',
        fontWeight: '400',
        color: '#64748B',
        borderBottomColor: '#E2E8F0',
        '&:hover': {
          backgroundColor: '#F8FAFC',
        },
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-semibold">Products</h2>
          <span className="text-sm text-gray-500">
            {filteredProducts.length} of {products.length} products
          </span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-bold text-lg" />
            Export
          </button>
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-download-simple font-bold text-lg" />
            Import
          </button>
          <button
            onClick={() => setIsAddProductOpen(true)}
            className="btn btn-primary inline-flex items-center gap-2"
          >
            <span className="icon icon-plus font-bold text-lg" />
            Add Product
          </button>
        </div>
      </div>

      {/* Status Tabs */}
      <div className="flex border-b border-border-color">
        {statusTabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveStatusTab(tab.id)}
            className={`flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeStatusTab === tab.id
                ? 'border-primary-500 text-primary-500'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            {tab.label}
            {tab.count !== null && (
              <span className={`px-2 py-0.5 text-xs rounded-full ${
                activeStatusTab === tab.id
                  ? 'bg-primary-100 text-primary-600'
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {tab.count}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Filters and Table */}
      <div className="bg-white rounded-xl border border-border-color">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
          placeholder="Search products..."
        />

        <DataTable
          columns={columns}
          data={filteredProducts}
          pagination
          paginationPerPage={10}
          paginationRowsPerPageOptions={[10, 20, 50, 100]}
          selectableRows
          onSelectedRowsChange={handleSelectedRowsChange}
          customStyles={customStyles}
          noDataComponent={
            <div className="flex flex-col items-center justify-center py-12">
              <span className="icon icon-package text-4xl text-gray-300 mb-4" />
              <p className="text-gray-500 text-lg font-medium">No products found</p>
              <p className="text-gray-400 text-sm">Try adjusting your search or filters</p>
            </div>
          }
        />
      </div>

      {/* Modals */}
      <ProductDetailModal
        isOpen={isProductDetailOpen}
        onClose={() => setIsProductDetailOpen(false)}
        product={selectedProduct}
      />

      <AddProductModal
        isOpen={isAddProductOpen}
        onClose={() => setIsAddProductOpen(false)}
        onAddProduct={handleAddProduct}
      />
    </div>
  );
};

export default ProductsTab;
