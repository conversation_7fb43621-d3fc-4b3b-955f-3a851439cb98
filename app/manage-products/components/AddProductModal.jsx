'use client';

import React, { useState } from 'react';
// import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const AddProductModal = ({ isOpen, onClose, onAddProduct }) => {
  const [activeSection, setActiveSection] = useState('general');
  const [formData, setFormData] = useState({
    // General Information
    productName: '',
    productType: 'Simple',
    productCode: '',
    category: '',
    brand: '',
    vendor: '',
    status: 'Draft',
    tags: [],
    thumbnail: null,
    
    // Description
    shortDescription: '',
    fullDescription: '',
    keyFeatures: [],
    productManual: null,
    
    // Pricing & Inventory
    mrp: '',
    sellingPrice: '',
    costPrice: '',
    inventory: '',
    minOrderQty: 1,
    inventoryThreshold: 10,
    inventoryTracking: 'Global',
    barcode: '',
    
    // Promotions
    discountType: 'None',
    discountValue: '',
    discountStartDate: '',
    discountEndDate: '',
    associatedCampaign: '',
    
    // Shipping & Logistics
    weight: '',
    dimensions: { length: '', width: '', height: '' },
    shippingCharges: '',
    deliveryTime: '2-5 business days',
    returnable: true,
    replaceable: true,
    
    // Policies
    returnPolicy: '',
    warrantyInfo: '',
    termsConditions: '',
    
    // SEO Meta
    metaTitle: '',
    metaDescription: '',
    metaKeywords: '',
    urlSlug: '',
    ogImage: null
  });

  const sections = [
    { id: 'general', label: 'General Info', icon: 'icon-info-circle' },
    { id: 'description', label: 'Description', icon: 'icon-file-text' },
    { id: 'pricing', label: 'Pricing & Inventory', icon: 'icon-dollar-sign' },
    { id: 'promotions', label: 'Promotions', icon: 'icon-tag' },
    { id: 'shipping', label: 'Shipping', icon: 'icon-truck' },
    { id: 'policies', label: 'Policies', icon: 'icon-shield-check' },
    { id: 'seo', label: 'SEO Meta', icon: 'icon-search' }
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const handleTagAdd = (tag) => {
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
    }
  };

  const handleTagRemove = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleFeatureAdd = (feature) => {
    if (feature && !formData.keyFeatures.includes(feature)) {
      setFormData(prev => ({
        ...prev,
        keyFeatures: [...prev.keyFeatures, feature]
      }));
    }
  };

  const handleFeatureRemove = (featureToRemove) => {
    setFormData(prev => ({
      ...prev,
      keyFeatures: prev.keyFeatures.filter(feature => feature !== featureToRemove)
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onAddProduct(formData);
    onClose();
    // Reset form
    setFormData({
      productName: '',
      productType: 'Simple',
      productCode: '',
      category: '',
      brand: '',
      vendor: '',
      status: 'Draft',
      tags: [],
      thumbnail: null,
      shortDescription: '',
      fullDescription: '',
      keyFeatures: [],
      productManual: null,
      mrp: '',
      sellingPrice: '',
      costPrice: '',
      inventory: '',
      minOrderQty: 1,
      inventoryThreshold: 10,
      inventoryTracking: 'Global',
      barcode: '',
      discountType: 'None',
      discountValue: '',
      discountStartDate: '',
      discountEndDate: '',
      associatedCampaign: '',
      weight: '',
      dimensions: { length: '', width: '', height: '' },
      shippingCharges: '',
      deliveryTime: '2-5 business days',
      returnable: true,
      replaceable: true,
      returnPolicy: '',
      warrantyInfo: '',
      termsConditions: '',
      metaTitle: '',
      metaDescription: '',
      metaKeywords: '',
      urlSlug: '',
      ogImage: null
    });
  };

  const renderGeneralInfo = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Vendor
          </label>
          <select
            name="productType"
            value={formData.productType}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          >
            <option value="Simple">Simple</option>
            <option value="Variant">Variant</option>
            <option value="Bundle">Bundle</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Product Name *
          </label>
          <input
            type="text"
            name="productName"
            value={formData.productName}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Product Type *
          </label>
          <select
            name="productType"
            value={formData.productType}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          >
            <option value="Simple">Simple</option>
            <option value="Variant">Variant</option>
            <option value="Bundle">Bundle</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Product Code/SKU *
          </label>
          <input
            type="text"
            name="productCode"
            value={formData.productCode}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Category *
          </label>
          <select
            name="category"
            value={formData.category}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          >
            <option value="">Select Category</option>
            <option value="Electronics">Electronics</option>
            <option value="Clothing">Clothing</option>
            <option value="Furniture">Furniture</option>
            <option value="Accessories">Accessories</option>
            <option value="Computers">Computers</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Brand
          </label>
          <select
            name="brand"
            value={formData.brand}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">Select Brand</option>
            <option value="TechSound">TechSound</option>
            <option value="ComfortWear">ComfortWear</option>
            <option value="ErgoDesk">ErgoDesk</option>
            <option value="ProtectTech">ProtectTech</option>
            <option value="GameForce">GameForce</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Status
          </label>
          <select
            name="status"
            value={formData.status}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="Draft">Draft</option>
            <option value="Published">Published</option>
            <option value="Pending Approval">Pending Approval</option>
            <option value="Archive">Archive</option>
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Product Tags
        </label>
        <div className="flex flex-wrap gap-2 mb-2">
          {formData.tags.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
            >
              {tag}
              <button
                type="button"
                onClick={() => handleTagRemove(tag)}
                className="text-blue-600 hover:text-blue-800"
              >
                ×
              </button>
            </span>
          ))}
        </div>
        <input
          type="text"
          placeholder="Add tag and press Enter"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleTagAdd(e.target.value.trim());
              e.target.value = '';
            }
          }}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Product Thumbnail
        </label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <span className="icon icon-image text-2xl text-gray-400 mb-2 block" />
          <p className="text-sm text-gray-500">
            Click to upload or drag and drop
          </p>
          <p className="text-xs text-gray-400">
            PNG, JPG up to 2MB (1:1 ratio recommended)
          </p>
          <input
            type="file"
            name="thumbnail"
            accept="image/*"
            className="hidden"
            onChange={handleInputChange}
          />
        </div>
      </div>
    </div>
  );

  const renderDescription = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Short Description
        </label>
        <textarea
          name="shortDescription"
          value={formData.shortDescription}
          onChange={handleInputChange}
          rows={3}
          maxLength={300}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder="Brief product description (max 300 characters)"
        />
        <div className="text-xs text-gray-500 mt-1">
          {formData.shortDescription.length}/300 characters
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Full Description
        </label>
        <textarea
          name="fullDescription"
          value={formData.fullDescription}
          onChange={handleInputChange}
          rows={6}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder="Detailed product description with HTML formatting support"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Key Features
        </label>
        <div className="space-y-2 mb-2">
          {formData.keyFeatures.map((feature, index) => (
            <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
              <span className="flex-1 text-sm">{feature}</span>
              <button
                type="button"
                onClick={() => handleFeatureRemove(feature)}
                className="text-red-600 hover:text-red-800"
              >
                <span className="icon icon-trash text-sm" />
              </button>
            </div>
          ))}
        </div>
        <input
          type="text"
          placeholder="Add feature and press Enter"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleFeatureAdd(e.target.value.trim());
              e.target.value = '';
            }
          }}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Product Manual (PDF)
        </label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <span className="icon icon-file-pdf text-2xl text-gray-400 mb-2 block" />
          <p className="text-sm text-gray-500">
            Click to upload product manual
          </p>
          <p className="text-xs text-gray-400">
            PDF up to 5MB
          </p>
          <input
            type="file"
            name="productManual"
            accept=".pdf"
            className="hidden"
            onChange={handleInputChange}
          />
        </div>
      </div>
    </div>
  );

  const renderPricingInventory = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            MRP/Original Price *
          </label>
          <input
            type="number"
            name="mrp"
            value={formData.mrp}
            onChange={handleInputChange}
            step="0.01"
            min="0"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Selling Price *
          </label>
          <input
            type="number"
            name="sellingPrice"
            value={formData.sellingPrice}
            onChange={handleInputChange}
            step="0.01"
            min="0"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Cost Price
          </label>
          <input
            type="number"
            name="costPrice"
            value={formData.costPrice}
            onChange={handleInputChange}
            step="0.01"
            min="0"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Inventory Quantity *
          </label>
          <input
            type="number"
            name="inventory"
            value={formData.inventory}
            onChange={handleInputChange}
            min="0"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Min Order Quantity
          </label>
          <input
            type="number"
            name="minOrderQty"
            value={formData.minOrderQty}
            onChange={handleInputChange}
            min="1"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Inventory Threshold
          </label>
          <input
            type="number"
            name="inventoryThreshold"
            value={formData.inventoryThreshold}
            onChange={handleInputChange}
            min="0"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Inventory Tracking
          </label>
          <select
            name="inventoryTracking"
            value={formData.inventoryTracking}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="Global">Global</option>
            <option value="Per Variant">Per Variant</option>
            <option value="Not Tracked">Not Tracked</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Barcode/UPC
          </label>
          <input
            type="text"
            name="barcode"
            value={formData.barcode}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
      </div>
    </div>
  );

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'general':
        return renderGeneralInfo();
      case 'description':
        return renderDescription();
      case 'pricing':
        return renderPricingInventory();
      case 'promotions':
        return <div className="text-center py-8 text-gray-500">Promotions section coming soon...</div>;
      case 'shipping':
        return <div className="text-center py-8 text-gray-500">Shipping section coming soon...</div>;
      case 'policies':
        return <div className="text-center py-8 text-gray-500">Policies section coming soon...</div>;
      case 'seo':
        return <div className="text-center py-8 text-gray-500">SEO Meta section coming soon...</div>;
      default:
        return renderGeneralInfo();
    }
  };

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Add New Product"
      size="lg"
    >
      <form onSubmit={handleSubmit} className="flex h-[calc(100dvh_-_117px)]">
        {/* Section Navigation */}
        <div className="w-64 border-r border-gray-200 p-4">
          <div className="space-y-1">
            {sections.map((section) => (
              <button
                key={section.id}
                type="button"
                onClick={() => setActiveSection(section.id)}
                className={`w-full flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  activeSection === section.id
                    ? 'bg-primary-50 text-primary-500 '
                    : 'text-gray-400 hover:bg-gray-50'
                }`}
              >
                <span className={`${section.icon} text-base`} />
                {section.label}
              </button>
            ))}
          </div>
        </div>

        {/* Section Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              {sections.find(s => s.id === activeSection)?.label}
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              Fill in the required information for this section
            </p>
          </div>

          {renderSectionContent()}
        </div>
      </form>

      {/* Action Buttons */}
      <div className="flex justify-between border-t border-border-color p-4">
        <button
          type="button"
          onClick={onClose}
          className="btn btn-outline-gray"
        >
          Cancel
        </button>
        <div className="flex gap-3">
          <button
            type="button"
            onClick={() => {
              const draftData = { ...formData, status: 'Draft' };
              onAddProduct(draftData);
              onClose();
            }}
            className="btn btn-gray"
          >
            Save as Draft
          </button>
          <button
            type="submit"
            className="btn btn-primary"
          >
            Add Product
          </button>
        </div>
      </div>
    </BaseOffCanvas>
  );
};

export default AddProductModal;
